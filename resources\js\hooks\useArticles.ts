import { useState, useEffect } from 'react';
import { articlesAPI, Article } from '../services/api';

interface UseArticlesOptions {
  category_id?: number;
  search?: string;
  status?: 'published' | 'draft';
  featured?: boolean;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

interface ArticlesResponse {
  data: Article[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

export const useArticles = (options: UseArticlesOptions = {}) => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 12,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchArticles = async (page: number = 1) => {
    setIsLoading(true);
    setError(null);

    try {
      const params = {
        ...options,
        page,
      };

      const response = await articlesAPI.getAll(params);
      const data: ArticlesResponse = response.data;

      setArticles(data.data);
      setPagination({
        current_page: data.current_page,
        last_page: data.last_page,
        per_page: data.per_page,
        total: data.total,
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erreur lors du chargement des articles');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchArticles();
  }, [
    options.category_id,
    options.search,
    options.status,
    options.featured,
    options.per_page,
    options.sort_by,
    options.sort_order,
  ]);

  const refetch = () => {
    fetchArticles(pagination.current_page);
  };

  const goToPage = (page: number) => {
    fetchArticles(page);
  };

  return {
    articles,
    pagination,
    isLoading,
    error,
    refetch,
    goToPage,
  };
};

export const useArticle = (id: string | number) => {
  const [article, setArticle] = useState<Article | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticle = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await articlesAPI.getById(id);
        setArticle(response.data);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Erreur lors du chargement de l\'article');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchArticle();
    }
  }, [id]);

  return {
    article,
    isLoading,
    error,
  };
};
