import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import type { CategoryStat } from '../../../pages/types/admin';

interface CategoriesChartProps {
  data: CategoryStat[];
}

const CategoriesChart: React.FC<CategoriesChartProps> = ({ data }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Répartition par catégories</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
label={({ name, percent }) => 
  `${name} ${percent ? (percent * 100).toFixed(0) : 0}%`
}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CategoriesChart;