import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Calendar, Clock, User, Share2, Facebook, Twitter, Linkedin, ArrowLeft, Tag } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import CommentSection from '../components/article/CommentSection';
import ArticleCard from '../../components/article/ArticleCard';
import articlesData from '../data/articles.json';
import type { Article } from '../types';

const ArticlePage: React.FC = () => {
  const { articleSlug } = useParams<{ articleSlug: string }>();
  
  const articles: Article[] = articlesData.articles;
  const article = articles.find(a => a.slug === articleSlug);

  if (!article) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Article non trouvé</h1>
          <p className="text-gray-600 mb-8">L'article demandé n'existe pas ou a été supprimé.</p>
          <Link
            to="/"
            className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour à l'accueil
          </Link>
        </div>
      </div>
    );
  }

  const relatedArticles = articles
    .filter(a => a.id !== article.id && (a.category === article.category || 
      a.tags.some(tag => article.tags.includes(tag))))
    .slice(0, 3);

  const shareUrl = window.location.href;
  const shareTitle = article.title;

  const handleShare = (platform: string) => {
    let url = '';
    switch (platform) {
      case 'facebook':
        url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
        break;
      case 'twitter':
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareTitle)}&url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'linkedin':
        url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
    }
    if (url) {
      window.open(url, '_blank', 'width=600,height=400');
    }
  };

  return (
    <div>
      {/* Navigation */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          to="/"
          className="inline-flex items-center text-green-600 hover:text-green-700 font-medium mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Retour aux articles
        </Link>
      </div>

      {/* Article Header */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Catégorie */}
        <div className="mb-4">
          <Link
            to={`/category/${articlesData.categories.find(c => c.name === article.category)?.slug}`}
            className="inline-block px-3 py-1 bg-green-500 text-white rounded-full text-sm font-medium hover:bg-green-600 transition-colors"
          >
            {article.category}
          </Link>
        </div>

        {/* Titre */}
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
          {article.title}
        </h1>

        {/* Métadonnées */}
        <div className="flex flex-wrap items-center justify-between mb-8 p-6 bg-gray-50 rounded-xl">
          <div className="flex flex-wrap items-center gap-6 text-gray-600">
            <div className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              <span className="font-medium">{article.author}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              <span>{format(new Date(article.date), 'dd MMMM yyyy', { locale: fr })}</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              <span>{article.readTime} min de lecture</span>
            </div>
          </div>

          {/* Boutons de partage */}
          <div className="flex items-center space-x-2 mt-4 md:mt-0">
            <Share2 className="w-5 h-5 text-gray-500 mr-2" />
            <button
              onClick={() => handleShare('facebook')}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              title="Partager sur Facebook"
            >
              <Facebook className="w-5 h-5" />
            </button>
            <button
              onClick={() => handleShare('twitter')}
              className="p-2 text-blue-400 hover:bg-blue-50 rounded-lg transition-colors"
              title="Partager sur Twitter"
            >
              <Twitter className="w-5 h-5" />
            </button>
            <button
              onClick={() => handleShare('linkedin')}
              className="p-2 text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
              title="Partager sur LinkedIn"
            >
              <Linkedin className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Image principale */}
        <div className="mb-8 rounded-xl overflow-hidden">
          <img
            src={article.image}
            alt={article.title}
            className="w-full h-64 md:h-96 object-cover"
          />
        </div>

        {/* Excerpt */}
        <div className="text-xl text-gray-700 mb-8 p-6 bg-green-50 rounded-xl border-l-4 border-green-500">
          <p className="font-medium italic">{article.excerpt}</p>
        </div>

        {/* Contenu */}
        <div className="prose prose-lg max-w-none mb-8">
          {article.content.split('\n\n').map((paragraph, index) => (
            <p key={index} className="mb-6 text-gray-800 leading-relaxed">
              {paragraph}
            </p>
          ))}
        </div>

        {/* Vidéo (si disponible) */}
        {article.videoUrl && (
          <div className="mb-8 rounded-xl overflow-hidden">
            <div className="aspect-w-16 aspect-h-9">
              <iframe
                src={article.videoUrl}
                title={`Vidéo - ${article.title}`}
                className="w-full h-64 md:h-96"
                allowFullScreen
              />
            </div>
          </div>
        )}

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="mb-12 p-6 bg-gray-50 rounded-xl">
            <div className="flex items-center mb-4">
              <Tag className="w-5 h-5 mr-2 text-gray-600" />
              <span className="font-semibold text-gray-900">Tags</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-white border border-gray-300 text-gray-700 text-sm rounded-full hover:border-green-500 hover:text-green-600 transition-colors cursor-pointer"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </article>

      {/* Section commentaires */}
      <CommentSection articleId={article.id} />

      {/* Articles liés */}
      {relatedArticles.length > 0 && (
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Articles similaires</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedArticles.map((relatedArticle) => (
              <ArticleCard key={relatedArticle.id} article={relatedArticle} />
            ))}
          </div>
        </section>
      )}
    </div>
  );
};

export default ArticlePage;