import React from 'react';

const TailwindTest: React.FC = () => {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-r from-green-400 to-blue-500">
            <h1 className="text-4xl font-bold text-white mb-4">Test Tailwind CSS</h1>
            <p className="text-lg text-gray-100">React + TypeScript + Tailwind fonctionne ✅</p>
            <button className="mt-6 px-6 py-2 bg-white text-blue-600 font-semibold rounded shadow hover:bg-gray-200 transition">
                Bouton Test
            </button>
        </div>
    );
};

export default TailwindTest;