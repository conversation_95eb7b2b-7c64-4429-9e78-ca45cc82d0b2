{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.0.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite": "^7.0.4"}, "name": "football-projet", "version": "1.0.0", "description": "<p align=\"center\"><a href=\"https://laravel.com\" target=\"_blank\"><img src=\"https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg\" width=\"400\" alt=\"Laravel Logo\"></a></p>", "main": "vite.config.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@heroicons/react": "^2.2.0", "date-fns": "^4.1.0", "lucide-react": "^0.539.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "recharts": "^3.1.2"}}