<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Article;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin
        $admin = User::create([
            'name' => 'Administrateur',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'is_active' => true,
            'bio' => 'Administrateur principal du site de football',
        ]);

        // Créer quelques catégories de base
        $categories = [
            [
                'name' => 'Actualités',
                'slug' => 'actualites',
                'description' => 'Toutes les dernières actualités du football',
                'color' => '#3B82F6',
                'icon' => 'newspaper',
                'sort_order' => 1,
            ],
            [
                'name' => 'Ligue 1',
                'slug' => 'ligue-1',
                'description' => 'Actualités et résultats de la Ligue 1',
                'color' => '#EF4444',
                'icon' => 'trophy',
                'sort_order' => 2,
            ],
            [
                'name' => 'Champions League',
                'slug' => 'champions-league',
                'description' => 'Toute l\'actualité de la Champions League',
                'color' => '#8B5CF6',
                'icon' => 'star',
                'sort_order' => 3,
            ],
            [
                'name' => 'Équipe de France',
                'slug' => 'equipe-de-france',
                'description' => 'Actualités de l\'équipe de France',
                'color' => '#10B981',
                'icon' => 'flag',
                'sort_order' => 4,
            ],
            [
                'name' => 'Transferts',
                'slug' => 'transferts',
                'description' => 'Mercato et transferts',
                'color' => '#F59E0B',
                'icon' => 'exchange',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Créer quelques articles d'exemple
        $articles = [
            [
                'title' => 'Bienvenue sur notre site de football',
                'excerpt' => 'Découvrez toute l\'actualité du football français et international sur notre nouveau site.',
                'content' => '<p>Bienvenue sur notre site dédié au football ! Vous trouverez ici toutes les dernières actualités, résultats et analyses du monde du football.</p><p>Notre équipe de journalistes passionnés vous propose un contenu de qualité pour suivre vos équipes et joueurs préférés.</p>',
                'category_id' => 1,
                'is_published' => true,
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Les derniers transferts de la Ligue 1',
                'excerpt' => 'Retrouvez tous les mouvements du mercato dans le championnat français.',
                'content' => '<p>Le mercato bat son plein en Ligue 1. Découvrez tous les transferts et rumeurs qui agitent le championnat français.</p>',
                'category_id' => 5,
                'is_published' => true,
                'published_at' => now()->subDays(1),
            ],
        ];

        foreach ($articles as $articleData) {
            $articleData['slug'] = Str::slug($articleData['title']);
            $articleData['author'] = $admin->name;
            $articleData['user_id'] = $admin->id;
            $articleData['tags'] = ['football', 'actualité'];

            Article::create($articleData);
        }

        $this->command->info('Utilisateur admin créé avec succès !');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Mot de passe: password123');
    }
}
