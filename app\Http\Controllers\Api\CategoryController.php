<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Liste des catégories
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::withCount('articles');

        // Filtrage pour le public (catégories actives seulement)
        if (!$request->user() || !$request->user()->isEditor()) {
            $query->active();
        }

        // Tri par ordre défini
        $query->ordered();

        $categories = $query->get();

        return response()->json($categories);
    }

    /**
     * Créer une nouvelle catégorie
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);

        // Vérifier l'unicité du slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (Category::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $category = Category::create($data);

        return response()->json([
            'message' => 'Catégorie créée avec succès',
            'category' => $category
        ], 201);
    }

    /**
     * Afficher une catégorie spécifique
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $query = Category::withCount('articles');

        // Si c'est un slug, chercher par slug, sinon par ID
        if (is_numeric($id)) {
            $category = $query->findOrFail($id);
        } else {
            $category = $query->where('slug', $id)->firstOrFail();
        }

        // Vérifier si la catégorie est active (sauf pour les admins)
        if (!$category->is_active && (!$request->user() || !$request->user()->isEditor())) {
            abort(404);
        }

        // Charger les articles de la catégorie
        $articlesQuery = $category->articles()->with(['user']);

        // Filtrer les articles publiés pour le public
        if (!$request->user() || !$request->user()->isEditor()) {
            $articlesQuery->published();
        }

        $articles = $articlesQuery->orderBy('published_at', 'desc')->paginate(12);

        return response()->json([
            'category' => $category,
            'articles' => $articles
        ]);
    }

    /**
     * Mettre à jour une catégorie
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $category = Category::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $data = $request->all();

        // Mettre à jour le slug si le nom a changé
        if ($request->name !== $category->name) {
            $data['slug'] = Str::slug($request->name);

            // Vérifier l'unicité du slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Category::where('slug', $data['slug'])->where('id', '!=', $category->id)->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $category->update($data);

        return response()->json([
            'message' => 'Catégorie mise à jour avec succès',
            'category' => $category
        ]);
    }

    /**
     * Supprimer une catégorie
     */
    public function destroy(string $id): JsonResponse
    {
        $category = Category::findOrFail($id);

        // Vérifier s'il y a des articles dans cette catégorie
        if ($category->articles()->count() > 0) {
            return response()->json([
                'message' => 'Impossible de supprimer une catégorie qui contient des articles'
            ], 422);
        }

        $category->delete();

        return response()->json([
            'message' => 'Catégorie supprimée avec succès'
        ]);
    }

    /**
     * Réorganiser l'ordre des catégories
     */
    public function reorder(Request $request): JsonResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                   ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'message' => 'Ordre des catégories mis à jour avec succès'
        ]);
    }
}
