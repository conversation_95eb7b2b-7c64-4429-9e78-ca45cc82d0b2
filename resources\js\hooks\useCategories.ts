import { useState, useEffect } from 'react';
import { categoriesAPI, Category } from '../services/api';

export const useCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await categoriesAPI.getAll();
      setCategories(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erreur lors du chargement des catégories');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const refetch = () => {
    fetchCategories();
  };

  return {
    categories,
    isLoading,
    error,
    refetch,
  };
};

export const useCategory = (id: string | number) => {
  const [category, setCategory] = useState<Category | null>(null);
  const [articles, setArticles] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategory = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await categoriesAPI.getById(id);
        setCategory(response.data.category);
        setArticles(response.data.articles);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Erreur lors du chargement de la catégorie');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchCategory();
    }
  }, [id]);

  return {
    category,
    articles,
    isLoading,
    error,
  };
};
