import React from 'react';
import { Link } from 'react-router-dom';
import ArticleCard from '../components/article/ArticleCard';
import Newsletter from '../components/common/Newsletter';
import { TrendingUp, Clock, Eye } from 'lucide-react';
import { useArticles } from '../hooks/useArticles';

const HomePage: React.FC = () => {
  const { articles: featuredArticles, isLoading: featuredLoading } = useArticles({
    featured: true,
    per_page: 3
  });

  const { articles: recentArticles, isLoading: recentLoading } = useArticles({
    per_page: 6,
    sort_by: 'published_at',
    sort_order: 'desc'
  });

  const { articles: trendingArticles, isLoading: trendingLoading } = useArticles({
    per_page: 3,
    sort_by: 'views_count',
    sort_order: 'desc'
  });

  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              L'actualité football<br />
              <span className="text-green-300">en temps réel</span>
            </h1>
            <p className="text-xl md:text-2xl text-green-100 mb-8 max-w-3xl mx-auto">
              Suivez toute l'actualité du football mondial, des analyses d'experts,
              et des statistiques exclusives sur FootballZone.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/category/actualites"
                className="bg-white text-green-700 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors"
              >
                Dernières actualités
              </Link>
              <Link
                to="/newsletter"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-700 transition-colors"
              >
                S'abonner à la newsletter
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Articles mis en avant */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-gray-900">À la une</h2>
          <Link
            to="/category/actualites"
            className="text-green-600 hover:text-green-700 font-medium flex items-center"
          >
            Voir tout
            <TrendingUp className="w-4 h-4 ml-1" />
          </Link>
        </div>

        {featuredLoading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {[1, 2].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        ) : featuredArticles.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredArticles.slice(0, 2).map((article) => (
              <ArticleCard key={article.id} article={article} variant="featured" />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Aucun article en vedette pour le moment.</p>
          </div>
        )}
      </section>

      {/* Articles récents et trending */}
      <section className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Articles récents */}
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Clock className="w-6 h-6 mr-2 text-green-600" />
                  Articles récents
                </h2>
                <Link
                  to="/category/actualites"
                  className="text-green-600 hover:text-green-700 font-medium"
                >
                  Voir plus
                </Link>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {recentArticles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Trending Articles */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <Eye className="w-5 h-5 mr-2 text-green-600" />
                  Tendances
                </h3>
                <div className="space-y-4">
                  {trendingArticles.map((article, index) => (
                    <div key={article.id} className="flex items-start space-x-3 group">
                      <span className="flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </span>
                      <div className="flex-1">
                        <Link
                          to={`/article/${article.slug}`}
                          className="text-gray-900 font-medium group-hover:text-green-600 transition-colors line-clamp-2"
                        >
                          {article.title}
                        </Link>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <Clock className="w-3 h-3 mr-1" />
                          {article.readTime} min
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Newsletter */}
              <Newsletter />

              {/* Catégories populaires */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-6">Catégories</h3>
                <div className="space-y-2">
                  {articlesData.categories.map((category) => (
                    <Link
                      key={category.slug}
                      to={`/category/${category.slug}`}
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                    >
                      <span 
                        className="text-sm font-medium"
                        style={{ color: category.color }}
                      >
                        {category.name}
                      </span>
                      <span className="text-xs text-gray-500 group-hover:text-gray-700">
                        {articles.filter(a => a.category === category.name).length} articles
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;