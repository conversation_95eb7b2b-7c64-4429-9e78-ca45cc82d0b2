<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class ArticleController extends Controller
{
    /**
     * Liste des articles (publics et admin)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Article::with(['category', 'user']);

        // Filtrage pour le public (articles publiés seulement)
        if (!$request->user() || !$request->user()->isEditor()) {
            $query->published();
        }

        // Recherche
        if ($request->has('search')) {
            $query->search($request->search);
        }

        // Filtrage par catégorie
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filtrage par statut (admin seulement)
        if ($request->user() && $request->user()->isEditor() && $request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // Articles en vedette
        if ($request->has('featured')) {
            $query->featured();
        }

        // Tri
        $sortBy = $request->get('sort_by', 'published_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 12);
        $articles = $query->paginate($perPage);

        return response()->json($articles);
    }

    /**
     * Créer un nouvel article
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'featured_image' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'tags' => 'nullable|array',
            'meta_data' => 'nullable|array',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['author'] = $request->user()->name;
        $data['user_id'] = $request->user()->id;

        // Si publié, définir la date de publication
        if ($request->is_published && !$request->published_at) {
            $data['published_at'] = now();
        }

        // Vérifier l'unicité du slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (Article::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $article = Article::create($data);
        $article->load(['category', 'user']);

        return response()->json([
            'message' => 'Article créé avec succès',
            'article' => $article
        ], 201);
    }

    /**
     * Afficher un article spécifique
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $query = Article::with(['category', 'user', 'comments' => function($q) {
            $q->approved()->root()->with('replies');
        }]);

        // Si c'est un slug, chercher par slug, sinon par ID
        if (is_numeric($id)) {
            $article = $query->findOrFail($id);
        } else {
            $article = $query->where('slug', $id)->firstOrFail();
        }

        // Vérifier si l'article est publié (sauf pour les admins)
        if (!$article->is_published && (!$request->user() || !$request->user()->isEditor())) {
            abort(404);
        }

        // Incrémenter le compteur de vues (seulement pour les visiteurs publics)
        if (!$request->user()) {
            $article->increment('views_count');
        }

        return response()->json($article);
    }

    /**
     * Mettre à jour un article
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $article = Article::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'featured_image' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'tags' => 'nullable|array',
            'meta_data' => 'nullable|array',
        ]);

        $data = $request->all();

        // Mettre à jour le slug si le titre a changé
        if ($request->title !== $article->title) {
            $data['slug'] = Str::slug($request->title);

            // Vérifier l'unicité du slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Article::where('slug', $data['slug'])->where('id', '!=', $article->id)->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Si on publie l'article pour la première fois
        if ($request->is_published && !$article->is_published && !$request->published_at) {
            $data['published_at'] = now();
        }

        $article->update($data);
        $article->load(['category', 'user']);

        return response()->json([
            'message' => 'Article mis à jour avec succès',
            'article' => $article
        ]);
    }

    /**
     * Supprimer un article
     */
    public function destroy(string $id): JsonResponse
    {
        $article = Article::findOrFail($id);
        $article->delete();

        return response()->json([
            'message' => 'Article supprimé avec succès'
        ]);
    }
}
