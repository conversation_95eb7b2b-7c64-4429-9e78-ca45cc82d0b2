import React from 'react';
import { useNavigate } from 'react-router-dom';
import ArticleForm from '../../components/admin/Articles/ArticleForm';
import type { ArticleFormData } from '../../pages/types/admin';

const AddArticlePage: React.FC = () => {
  const navigate = useNavigate();

  const handleSubmit = (data: ArticleFormData) => {
    // Ici, vous feriez un appel API pour créer l'article
    console.log('Création d\'un nouvel article:', data);
    
    // Simulation de la création
    setTimeout(() => {
      alert('Article créé avec succès !');
      navigate('/admin/articles');
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Nouvel article</h1>
        <p className="text-gray-600">Créez un nouvel article pour votre journal</p>
      </div>

      {/* Form */}
      <ArticleForm onSubmit={handleSubmit} />
    </div>
  );
};

export default AddArticlePage;