<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ArticleController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CommentController;
use App\Http\Controllers\Api\SubscriberController;
use App\Http\Controllers\Api\MediaController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques
Route::prefix('v1')->group(function () {
    // Authentification
    Route::post('/auth/login', [AuthController::class, 'login']);
    
    // Articles publics
    Route::get('/articles', [ArticleController::class, 'index']);
    Route::get('/articles/{id}', [ArticleController::class, 'show']);
    
    // Catégories publiques
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/categories/{id}', [CategoryController::class, 'show']);
    
    // Commentaires publics
    Route::get('/articles/{article}/comments', [CommentController::class, 'index']);
    Route::post('/articles/{article}/comments', [CommentController::class, 'store']);
    
    // Newsletter
    Route::post('/newsletter/subscribe', [SubscriberController::class, 'subscribe']);
    Route::post('/newsletter/unsubscribe', [SubscriberController::class, 'unsubscribe']);
});

// Routes protégées (admin)
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // Authentification
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/me', [AuthController::class, 'me']);
    Route::put('/auth/profile', [AuthController::class, 'updateProfile']);
    Route::put('/auth/password', [AuthController::class, 'changePassword']);
    
    // Articles (CRUD complet)
    Route::apiResource('articles', ArticleController::class)->except(['index', 'show']);
    
    // Catégories (CRUD complet)
    Route::apiResource('categories', CategoryController::class)->except(['index', 'show']);
    Route::post('/categories/reorder', [CategoryController::class, 'reorder']);
    
    // Commentaires (gestion admin)
    Route::apiResource('comments', CommentController::class)->except(['index', 'store']);
    Route::put('/comments/{comment}/approve', [CommentController::class, 'approve']);
    Route::put('/comments/{comment}/reject', [CommentController::class, 'reject']);
    
    // Abonnés newsletter
    Route::apiResource('subscribers', SubscriberController::class);
    
    // Médias
    Route::apiResource('media', MediaController::class);
    Route::post('/media/upload', [MediaController::class, 'upload']);
});
